"use client";

import React, { useState, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Loader2,
  User as UserIcon,
  MapPin,
  CreditCard,
  Users,
} from "lucide-react";
import { User } from "@/constants/user";
import { useRouter } from "next/router";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "@/components/ui/use-toast";
import { logoutMethod } from "@/utils/auth";
import { signOut } from "next-auth/react";
import { clearReduxOnLogout } from "@/store/clearRedux";
import { useCustomSession } from "@/hooks/use-custom-session";

interface SidebarProps {
  activeSection: string;
  setActiveSection: (section: string) => void;
  user: User | null;
  updateUser?: (data: User) => Promise<void>;
}

const Sidebar: React.FC<SidebarProps> = ({
  activeSection,
  setActiveSection,
  user,
  updateUser,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const { data: session } = useCustomSession();
  const token = session?.accessToken;

  const handleLogout = async () => {
    const response = await logoutMethod("auth/signout", token);
    if (response.success) {
      await clearReduxOnLogout();
      signOut({ redirect: false });
      router.push("/");
    }
  };

  const sections = [
    {
      option: "User Profile",
      path: "/profile",
      icon: UserIcon,
    },
    {
      option: "Address Details",
      path: "/address",
      icon: MapPin,
    },
    {
      option: "Manage Booking",
      path: "/manage-booking",
      icon: CreditCard,
    },
    {
      option: "Passenger Profiles",
      path: "/passengers",
      icon: Users,
    },
    {
      option: "Sign out",
      path: "/logout",
      icon: CreditCard,
    },
  ];

  const convertToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        if (typeof reader.result === "string") {
          resolve(reader.result);
        } else {
          reject(new Error("Failed to convert to base64"));
        }
      };
      reader.onerror = (error) => reject(error);
    });
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (files && files.length > 0 && user && updateUser) {
      try {
        setErrorMessage(null);

        const fileSize = files[0].size / 1024 / 1024;
        if (fileSize > 5) {
          setErrorMessage("Image size exceeds 5MB limit");
          toast({
            title: "Error",
            description:
              "Image size exceeds 5MB limit. Please choose a smaller image.",
            variant: "destructive",
          });
          return;
        }

        setIsUploading(true);

        const reader = new FileReader();
        reader.onload = (e) => {
          if (e.target?.result) {
            setPreviewImage(e.target.result as string);
          }
        };
        reader.readAsDataURL(files[0]);

        const base64String = await convertToBase64(files[0]);

        const updatedUser: User = {
          ...user,
          filedata: base64String,
        };

        await updateUser(updatedUser);
      } catch (error) {
        console.error("Error uploading profile picture:", error);
        setErrorMessage("Failed to upload image");
        toast({
          title: "Error",
          description: "Failed to upload image. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsUploading(false);
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    }
  };

  const handleEditPictureClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleSectionClick = (option: string) => {
    if (option === "Sign out") {
      handleLogout();
    } else {
      setActiveSection(option);
    }
  };

  const getInitials = () => {
    const firstName = user?.firstName || "";
    const lastName = user?.lastName || "";
    return (
      (firstName[0]?.toUpperCase() || "") + (lastName[0]?.toUpperCase() || "")
    );
  };

  const avatarSrc = previewImage || user?.profile_picture || "";
  const hasValidImage = avatarSrc && avatarSrc.length > 0;

  return (
    <div className="hidden md:h-screen md:block font-proxima-nova items-center md:items-end bg-no-repeat bg-cover bg-center rounded-2xl">
      <div className="flex flex-row md:flex-col py-4 md:space-y-4 space-x-4 md:space-x-0 h-full justify-between md:justify-start items-center md:items-stretch">
        <div className="flex flex-col items-center md:mb-2 lg:mb-4">
          <div
            className="relative cursor-pointer"
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept="image/*"
              className="hidden"
            />

            <div className="md:w-[100px] md:h-[100px] w-[60px] h-[60px]">
              <div className="relative w-full h-full">
                {isUploading ? (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 rounded-full z-20">
                    <div className="flex flex-col items-center space-y-2">
                      <Loader2 className="h-8 w-8 animate-spin" />
                      <span className="text-xs">Uploading...</span>
                    </div>
                  </div>
                ) : isHovered ? (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded-full z-20">
                    <button
                      className="flex items-center justify-center gap-1 bg-[#4B4BC3] border border-white px-3 py-1 rounded-full text-xs shadow-md"
                      onClick={handleEditPictureClick}
                      disabled={isUploading}
                    >
                      <PencilLine className="w-3 h-3" /> Edit
                    </button>
                  </div>
                ) : null}

                <Avatar className="w-full h-full">
                  {hasValidImage && (
                    <AvatarImage
                      src={avatarSrc}
                      alt="Profile"
                      className="object-cover"
                    />
                  )}
                  <AvatarFallback className="text-2xl md:text-3xl lg:text-4xl bg-gradient-to-r from-[#4B4BC3] to-[#707FF5]">
                    {getInitials()}
                  </AvatarFallback>
                </Avatar>
              </div>
            </div>

            {errorMessage && (
              <div className="text-red-500 text-xs mt-1 text-center">
                {errorMessage}
              </div>
            )}
          </div>
          <div className="flex items-center mt-2">
            <div className="px-2 text-sm md:text-base lg:text-xl xl:text-2xl font-bold text-brand-black">
              {user?.firstName + " " + user?.lastName || "User"}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-grow flex flex-col w-full pl-1 md:pl-2 lg:pl-4">
          {sections.map((section, index) => {
            const Icon = section.icon;
            const isActive = activeSection === section.option;

            return (
              <div
                key={index}
                onClick={() => handleSectionClick(section.option)}
                className={`flex flex-row items-center cursor-pointer my-1 md:my-3 px-1 md:px-2 lg:px-4 py-2 ${
                  isActive
                    ? "font-semibold text-white bg-brand md:rounded-tl-md md:rounded-bl-md md:rounded-tr-none md:rounded-br-none w-full"
                    : "text-brand-black w-full"
                }`}
              >
                <Icon
                  className={`w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 mr-1 md:mr-2 lg:mr-3 flex-shrink-0 ${
                    isActive ? "stroke-white" : "stroke-[#000B33]"
                  }`}
                  strokeWidth={2}
                  fill="none"
                />
                <div className="text-xs md:text-sm lg:text-base xl:text-lg truncate">
                  {section.option}
                </div>
              </div>
            );
          })}
        </div>

        <div className="h-10"></div>
      </div>
    </div>
  );
};

export default Sidebar;
