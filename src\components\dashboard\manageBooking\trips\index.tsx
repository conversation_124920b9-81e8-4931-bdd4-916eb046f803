import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import TripCard from "./tripCard";
import FlightInformation from "./flightInformation";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import axios from "axios";
import { useCustomSession } from "@/hooks/use-custom-session";
import { Loader2 } from "lucide-react";

const Trips = () => {
  const [activeTrip, setActiveTrip] = useState("trip-001");
  const [selectedTripId, setSelectedTripId] = useState<string | null>(null);
  const [selectedTripStatus, setSelectedTripStatus] = useState<
    "confirmed" | "pending" | "cancelled" | "completed" | null
  >(null);
  const [showFlightInfo, setShowFlightInfo] = useState(false);
  const [completedTrips, setCompletedTrips] = useState<any[]>([]);
  const [upcomingTrips, setUpcomingTrips] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  function handleViewTrip(
    tripId: string,
    status?: "confirmed" | "pending" | "cancelled" | "completed"
  ): void {
    setSelectedTripId(tripId);
    setSelectedTripStatus(status || null);
    setShowFlightInfo(true);
    // You can now use the status parameter here
    // console.log("Trip ID:", tripId, "Status:", status);
  }

  function handleBackToTrips(): void {
    setShowFlightInfo(false);
    setSelectedTripId(null);
  }

  // Get flight data for the selected trip
  const getFlightDataForTrip = (tripId: string) => {
    // Search in upcomingTrips and completedTrips
    const trip =
      upcomingTrips.find((t) => t.id === tripId) ||
      completedTrips.find((t) => t.id === tripId);
    if (!trip) return {};
    return trip;
  };

  // Default trips list view
  const { data: session, status } = useCustomSession();
  const token = session?.accessToken;

  const fetchUserDetails = async () => {
    try {
      if (!token) {
        setLoading(false);
        return;
      }

      setLoading(true);
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_AGENT_API_ENDPOINT}/api/v1/flight/trip-details`,
        {
          headers: {
            Authorization: `Bearer ${session?.accessToken || token}`,
            Accept: "application/json",
          },
        }
      );

      if (response.status !== 200) {
        setLoading(false);
        throw new Error("Failed to fetch user details");
      }

      const data = await response.data;
      // console.log(data);
      // console.log(data.detail.data?.CompletedTrips);
      // console.log(data.detail.data?.UpcomingTrips);
      setCompletedTrips(data.detail.data?.CompletedTrips || []);
      setUpcomingTrips(data.detail.data?.UpcomingTrips || []);
      setLoading(false);

      // dispatch(setCurrentUser(data?.detail?.data as UserDetails));
    } catch (error: any) {
      if (error.status === 401) {
        console.log("I am unauthorized, logout now");
        // handleInvalidSession();
      }
      console.error("Error fetching user details:", error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserDetails();
  }, [token]);

  console.log(upcomingTrips);

  return (
    <>
      {showFlightInfo && selectedTripId ? (
        <div className="relative">
          <FlightInformation
            {...getFlightDataForTrip(selectedTripId)}
            handleBackToTrips={handleBackToTrips}
            status={selectedTripStatus}
          />
        </div>
      ) : (
        <div>
          <Tabs defaultValue="Upcoming-trips" className="">
            <TabsList className="rounded-full h-10 p-0 mb-3 bg-neutral">
              <TabsTrigger
                value="Upcoming-trips"
                className="rounded-full font-semibold h-10 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
              >
                Upcoming trips
              </TabsTrigger>
              <TabsTrigger
                value="Past-trips"
                className="rounded-full font-semibold h-10 px-6 data-[state=active]:bg-brand data-[state=active]:text-white data-[state=inactive]:bg-transparent data-[state=inactive]:text-brand-grey transition-all duration-200"
              >
                Past trips
              </TabsTrigger>
            </TabsList>

            {/* Upcoming Trips */}
            <TabsContent value="Upcoming-trips">
              {loading ? (
                <div className="flex flex-col items-center justify-center min-h-[120px]">
                  <Loader2 className="mr-2 h-8 w-8 animate-spin" />
                  <span className="mt-2 text-gray-500">Loading...</span>
                </div>
              ) : (
                upcomingTrips.map((trip) => (
                  <TripCard
                    key={trip.id}
                    id={trip.id}
                    status={trip.status || "Confirmed"}
                    type={trip.type}
                    title={trip.location}
                    datesrc={trip.flightDetails.source.date}
                    datedes={trip.flightDetails.destination.date}
                    duration={trip.flightDetails.duration}
                    isActive={activeTrip === trip.id}
                    onViewTrip={handleViewTrip}
                  />
                ))
              )}
            </TabsContent>

            {/* Past Trips */}
            <TabsContent value="Past-trips">
              {loading ? (
                <div className="flex flex-col items-center justify-center min-h-[120px]">
                  <Loader2 className="mr-2 h-8 w-8 animate-spin" />
                  <span className="mt-2 text-gray-500">Loading...</span>
                </div>
              ) : (
                completedTrips.map((trip) => (
                  <TripCard
                    // key={trip.id}
                    // id={trip.id}
                    // status={trip.status || 'Complete'}
                    // type={trip.type}
                    // title={trip.title}
                    // date={trip.date}
                    // duration={trip.duration}
                    // onViewTrip={handleViewTrip}

                    key={trip.id}
                    id={trip.id}
                    status={trip.status || "Completed"}
                    type={trip.type}
                    title={trip.location}
                    datesrc={trip.flightDetails.source.date}
                    datedes={trip.flightDetails.destination.date}
                    duration={trip.flightDetails.duration}
                    isActive={activeTrip === trip.id}
                    onViewTrip={handleViewTrip}
                  />
                ))
              )}
            </TabsContent>
          </Tabs>
        </div>
      )}
    </>
  );
};

export default Trips;
