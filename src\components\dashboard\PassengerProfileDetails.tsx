import React, { useState, useEffect } from "react";
import { User, ChevronDown, PencilLine, UserIcon } from "lucide-react";
import { User as UserType, UserDetailDelete } from "@/constants/user";
import { Button } from "@/components/ui/button";
import UnifiedPassengerProfileForm from "./UnifiedPassengerProfileForm";
import ConfirmDeleteModal from "./ConfirmDeleteModal";

interface PassengerProfileDetailsProps {
  user: UserType | null;
  updateUser: (data: UserType) => Promise<void>;
  updatedStatus: string | null;
  deleteApi: (data: UserDetailDelete) => Promise<void>;
}

const formatDateWithDateObject = (dateString: string) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toISOString().split('T')[0]; // Returns YYYY-MM-DD format
};

const PassengerProfileDetails: React.FC<PassengerProfileDetailsProps> = ({
  user,
  updateUser,
  updatedStatus,
  deleteApi
}) => {
  const [addingProfile, setAddingProfile] = useState<boolean>(false);
  const [editingProfile, setEditingProfile] = useState<any | null>(null);
  const [expandedId, setExpandedId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [deleteProfileId, setDeleteProfileId] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);

  // Initialize with empty array if user or passengers is undefined
  const passengers = user?.passengers || [];

  // Format profiles for display
  const formatProfilesForDisplay = () => {
    if (!passengers || passengers.length === 0) return [];

    return passengers
      .filter(passenger => passenger) // Filter out any null passengers
      .map(passenger => {
        // Check if we have the new structure (with profile) or old structure (with info)
        const passengerInfo = passenger.profile || passenger.info;
        const passengerPreference = passenger.preference;

        if (!passengerInfo) return null;

        return {
          id: passenger.passenger_id,
          name: `${passengerInfo.title || ''} ${passengerInfo.firstName || ''} ${passengerInfo.lastName || ''}`.trim(),
          profile: {
            label: `${passengerInfo.title || ''} ${passengerInfo.firstName || ''} ${passengerInfo.lastName || ''}`.trim(),
            userName: `${passengerInfo.firstName || ''} ${passengerInfo.middleName ? passengerInfo.middleName + ' ' : ''}${passengerInfo.lastName || ''}`.trim(),
            userType: passengerPreference?.travelerType || "adult",
            dob: passengerInfo.dateOfBirth || '',
            country: passengerInfo.nationality || '',
            gender: passengerInfo.gender || ''
          }
        };
      })
      .filter(profile => profile !== null); // Remove any null profiles
  };

  const handleEditProfile = (profileId: string) => {
    // Find the original profile data from passengers array
    const profileToEdit = passengers.find(p => p.passenger_id === profileId);
    if (profileToEdit) {
      setEditingProfile(profileToEdit);
    }
  };

  // Map passenger data to format expected by UnifiedPassengerProfileForm
  const mapPassengerToFormData = (passenger: any) => {
    // Handle both old and new API response formats
    const passengerInfo = passenger.profile || passenger.info;
    const passengerContact = passenger.contact;
    const passengerPreference = passenger.preference;
    const passengerDocument = passenger.document;

    if (!passengerInfo) return null;

    return {
      id: passenger.passenger_id,
      title: passengerInfo.title || "",
      firstName: passengerInfo.firstName || "",
      middleName: passengerInfo.middleName || "",
      lastName: passengerInfo.lastName || "",
      nationality: passengerInfo.nationality || "",
      gender: passengerInfo.gender || "",
      dob: passengerInfo.dateOfBirth || "",
      email: passengerContact?.primaryEmail || "",
      mobile: passengerContact?.Phone || "",
      travelerType: passengerPreference?.travelerType || "",
      preferredSeat: passengerPreference?.preferredSeat || "",
      mealPreference: passengerPreference?.mealPreference || "",
      cabinClass: passengerPreference?.cabinClass || "",
      frequentFlyerNumber: passengerPreference?.frequentFlyerNumber || "",
      passportNumber: passengerDocument?.passport?.passportNumber || "",
      passportExpiry: passengerDocument?.passport?.passportExpiryDate || "",
      passportCountry: passengerDocument?.passport?.issueCountry || "",
      aadharNumber: passengerDocument?.aadhar?.aadharNumber || ""
    };
  };

  // Handle saving or updating passenger profile
  async function savePassengerData(formData: any) {
    if (!user) return;

    setLoading(true);

    try {
      // Check if it's an edit or a new profile
      const isEditing = editingProfile !== null;

      // Create the base passenger data structure
      const basePassengerData = {
        info: {
          title: formData.title,
          firstName: formData.firstName,
          middleName: formData.middleName || "",
          lastName: formData.lastName,
          dateOfBirth: formData.dob,
          gender: formData.gender,
          nationality: formData.nationality
        },
        contact: {
          primaryEmail: formData.email || "",
          Phone: formData.mobile || ""
        },
        preference: {
          travelerType: formData.travelerType || "",
          preferredSeat: formData.preferredSeat || "",
          mealPreference: formData.mealPreference || "",
          cabinClass: formData.cabinClass || "",
          frequentFlyerNumber: formData.frequentFlyerNumber || ""
        },
        document: {
          passport: {
            passportNumber: formData.passportNumber || "",
            passportExpiryDate: formData.passportExpiry || "",
            issueCountry: formData.passportCountry || ""
          },
          aadhar: {
            aadharNumber: formData.aadharNumber || ""
          }
        }
      };

      // Add IDs only for existing passenger profiles
      const passengerData = isEditing ? {
        passenger_id: editingProfile.passenger_id,
        info: {
          ...basePassengerData.info,
          info_id: editingProfile.info?.info_id || editingProfile.profile?.info_id || ""
        },
        contact: {
          ...basePassengerData.contact,
          contact_id: editingProfile.contact?.contact_id || ""
        },
        preference: {
          ...basePassengerData.preference,
          preference_id: editingProfile.preference?.preference_id || ""
        },
        document: {
          ...basePassengerData.document,
          document_id: editingProfile.document?.document_id || ""
        }
      } : basePassengerData;

      // Update the user object with the new/updated passenger
      const updatedPassengers = isEditing
        ? passengers.map(p => (p.passenger_id === editingProfile.passenger_id ? passengerData : p))
        : [...passengers, passengerData];

      const updatedUser = {
        ...user,
        passengers: updatedPassengers
      };

      // Call the update function
      await updateUser(updatedUser);

      // Wait for updated user to be fetched from parent before hiding the form
      setTimeout(() => {
        setEditingProfile(null);
        setAddingProfile(false);
        setLoading(false);
      }, 300);

    } catch (error) {
      console.error("Error saving passenger profile:", error);
      setLoading(false);
    }
  }

  // Handle delete confirmation
  const handleConfirmDelete = async () => {
    if (deleteProfileId) {
      await deleteApi({ passenger_id: deleteProfileId });
      setModalOpen(false);
      setDeleteProfileId(null);
    }
  };

  // Handle delete cancellation
  const handleCancelDelete = () => {
    setModalOpen(false);
    setDeleteProfileId(null);
  };

  // Format profiles for display
  const displayProfiles = formatProfilesForDisplay();

  // Check for empty or missing passengers array
  const hasPassengers = Array.isArray(passengers) && passengers.length > 0;

  return (
    <div className="w-full p-2 md:p-6">
      <ConfirmDeleteModal
        open={modalOpen}
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        message="Are you sure you want to delete this passenger profile?"
      />

      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-10 w-10 border-t-4 border-blue-500 border-solid"></div>
        </div>
      ) : addingProfile || editingProfile ? (
        <UnifiedPassengerProfileForm
          user={user}
          profile={editingProfile ? (mapPassengerToFormData(editingProfile) || undefined) : undefined}
          onSave={savePassengerData}
          onCancel={() => {
            setEditingProfile(null);
            setAddingProfile(false);
          }}
          isEdit={!!editingProfile}
        />
      ) : (
        <>
          {/* Show add form if no profiles exist */}
          {!hasPassengers ? (
            <div className="flex flex-col items-center justify-center min-h-[300px]">
              <div className="text-xl font-semibold text-center text-brand-black w-full">There are no passenger profiles registered for your account.</div>
              <div className="text-xl font-bold text-brand-black my-8 text-center">You can add a new passenger profile here</div>
              <Button

                className="text-sm text-brand-white bg-brand hover:text-brand-white hover:bg-brand px-6 py-4 rounded-full"
                onClick={() => setAddingProfile(true)}
              >
                Add New Passenger
              </Button>
            </div>
          ) : (
            // Saved Passenger Profiles Section
            <div className="space-y-6 md:space-y-10">
              <div className="flex flex-row sm:flex-row justify-between items-center gap-2">
                <h2 className="text-sm md:text-lg lg:text-3xl font-semibold text-brand-black">Saved Passenger Profiles</h2>
                <Button
                  variant="outline"
                  className="text-xs md:text-sm text-brand-white bg-brand hover:bg-brand hover:text-brand-white px-4 md:px-6 py-2 md:py-4 rounded-[8px] whitespace-nowrap"
                  onClick={() => setAddingProfile(true)}
                  disabled={displayProfiles.length >= 8}
                >
                  Add New Passenger
                </Button>
              </div>

              {displayProfiles.length > 0 ? (
                <div className="grid  gap-4  mt-4 md:mt-6">
                  {displayProfiles.map((profile) => (
                    <div key={profile?.id} className="flex flex-col">
                      <div className="relative font-proxima-nova w-full p-px border border-neutral rounded-sm h-auto bg-brand-white shadow-sm mb-4">
                        {expandedId === profile?.id ? (
                          /* Expanded card */
                          <div className="bg-brand-white  rounded-sm w-full h-full p-4 flex flex-col justify-between" style={{ minHeight: 280 }}>
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <UserIcon fill="#999999" color="#999999" className="w-5 h-5 md:w-6 md:h-6" />
                                <span className="font-semibold text-base md:text-lg text-neutral-dark ml-2 md:ml-4 truncate">{profile.name}</span>
                              </div>
                              <button
                                className="text-neutral-dark text-xl md:text-2xl"
                                onClick={() => setExpandedId(null)}
                              >
                                <ChevronDown className="text-neutral-dark transform rotate-180 w-5 h-5 md:w-6 md:h-6" />
                              </button>
                            </div>
                            <div className="mt-4 md:mt-6 text-sm md:text-base">
                              <div className="text-neutral-dark mb-1 truncate">{profile.profile.userName}</div>
                              <div className="text-neutral-dark mb-1">Type: {profile.profile.userType}</div>
                              <div className="text-neutral-dark mb-1">Gender: {profile.profile.gender}</div>
                              <div className="text-neutral-dark mb-1">DOB: {formatDateWithDateObject(profile.profile.dob)}</div>
                              <div className="text-neutral-dark mb-1">Nationality: {profile.profile.country}</div>
                            </div>
                            <div className="flex flex-col md:flex-row gap-2 md:gap-4 mt-4 md:mt-8">
                              <button
                                className="bg-white text-brand border border-brand rounded-[8px] px-4 md:px-6 py-1 md:py-2 text-xs md:text-sm w-full md:w-auto"
                                onClick={() => {
                                  if (profile.id) {
                                    setDeleteProfileId(profile.id);
                                    setModalOpen(true);
                                  }
                                }}
                              >
                                Delete
                              </button>
                              <button
                                className="bg-brand text-brand-white rounded-[8px]  px-4 md:px-6 py-1 md:py-2 text-xs md:text-sm w-full md:w-auto flex items-center justify-center gap-2"
                                onClick={() => profile.id ? handleEditProfile(profile.id) : null}
                              >
                                <PencilLine size={16} className="text-white w-3 h-3 md:w-4 md:h-4" />
                                Edit
                              </button>
                            </div>
                          </div>
                        ) : (
                          /* Collapsed card */
                          <div
                            className="bg-brand-white rounded-sm px-3 md:px-4 py-2 flex items-center gap-2 md:gap-4 shadow-md cursor-pointer justify-between border-1 border-neutral"
                            onClick={() => profile?.id ? setExpandedId(profile.id) : null}
                          >
                            <div className="flex items-center">
                              <UserIcon fill="#999999" color="#999999" className="w-5 h-5 md:w-6 md:h-6" />
                              <span className="font-semibold text-base md:text-lg text-neutral-dark ml-2 md:ml-4 truncate">{profile?.name}</span>
                            </div>
                            <ChevronDown className="text-neutral-dark w-5 h-5 md:w-6 md:h-6 flex-shrink-0" />
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4 bg-gray-100 rounded">
                  <UserIcon fill="black" color="black" className="w-12 h-12 mx-auto text-neutral-dark mb-2" />
                  <p className="text-gray-600">No passenger profiles found. Click 'Add New Passenger' to create one.</p>
                </div>
              )}
              <p className="text-sm text-neutral-dark italic mt-6 ml-1">
                * You can store a maximum of 8 passenger profiles.
              </p>
            </div>
          )}
        </>
      )}

      {updatedStatus === "success" && !loading ? (
        <p className="text-green-600 text-sm w-full p-2 text-center my-6">
          Passenger Profile Saved Successfully
        </p>
      ) : null}
    </div>
  );
};

export default PassengerProfileDetails;